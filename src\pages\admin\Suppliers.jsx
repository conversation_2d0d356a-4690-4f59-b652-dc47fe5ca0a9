import React, { useState } from "react";

const SuppliersPage = () => {
  const [search, setSearch] = useState("");
  const [suppliers] = useState([
    "Acme Corp",
    "Globex Inc",
    "Initech",
    "Umbrella Corp",
    "Stark Industries",
    "Wayne Enterprises",
    "Wonka Industries",
    "Aperture Science",
    "Hooli",
    "Massive Dynamic",
  ]);

  const filteredSuppliers = suppliers.filter((supplier) =>
    supplier.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="p-4 sm:p-6">
      <div className="flex justify-between items-start mb-4">
        <h1 className="text-2xl font-semibold">Suppliers</h1>
        <input
          type="text"
          placeholder="Search by supplier name or email"
          className="border-b border-purple-400 focus:outline-none focus:border-purple-600 text-sm px-1 w-64"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      <div className="border border-purple-300 rounded-lg shadow-md overflow-y-auto max-h-[400px]">
        <div className="bg-purple-50 px-3 py-2 border-b border-purple-300 font-medium">
          Supplier name
        </div>
        {filteredSuppliers.length > 0 ? (
          filteredSuppliers.map((name, i) => (
            <div
              key={i}
              className="px-3 py-2 border-b border-purple-100 hover:bg-purple-50"
            >
              {name}
            </div>
          ))
        ) : (
          <div className="px-3 py-4 text-gray-500">No suppliers found.</div>
        )}
      </div>
    </div>
  );
};

export default SuppliersPage;
