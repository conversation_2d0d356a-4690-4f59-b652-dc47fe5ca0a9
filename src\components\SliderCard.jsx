import React, { useRef } from "react";
import { FaChevronRight, FaChevronLeft } from "react-icons/fa";
import { motion } from "framer-motion";

const CardSlider = () => {
  const sliderRef = useRef(null);

  const scrollLeft = () => {
    sliderRef.current.scrollBy({ left: -400, behavior: "smooth" });
  };

  const scrollRight = () => {
    sliderRef.current.scrollBy({ left: 400, behavior: "smooth" });
  };

  const commonProps = {
    viewport: { once: false, amount: 0.4 }, // Animate once when 60% of element is in view
    transition: { duration: 0.8, ease: "easeOut" },
    className: "p-8 rounded-lg shadow-xl max-w-md text-center mx-auto",
  };

  const cards = [
    {
      title: "PR Import / Manual Entry",
      desc: "Start with quick PR import or manual input.",
      link: "See PR Import / Manual Entry",
    },
    {
      title: "Supplier Notification",
      desc: "Notify suppliers instantly.",
      link: "See Supplier Notification",
    },
    {
      title: "RFQ Lifecycle Stages",
      desc: "Track RFQs from draft to award.",
      link: "RFQ Lifecycle Stages",
    },
    {
      title: "Quote Analysis",
      desc: "Analyze and compare quotes quickly.",
      link: "Explore Quote Analysis",
    },
    {
      title: "Award Recommendations",
      desc: "Get smart award suggestions.",
      link: "Learn More",
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }} /* Starts invisible and 50px down */
      whileInView={{
        opacity: 1,
        y: 0,
      }} /* Animates to visible and original position */
      {...commonProps}
      className="relative max-w-[1440px] mx-auto px-4 sm:px-6 py-12"
    >
      <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6">
        Your RFQ Process,
        <p className="mt-2">Simplified & Automated</p>
      </h2>

      {/* Slider Controls - visible on md and up */}
      <div className="hidden md:flex justify-end gap-4 mb-4">
        <button
          onClick={scrollLeft}
          className="bg-violet-600 text-white p-3 rounded-full hover:bg-violet-700 transition"
        >
          <FaChevronLeft />
        </button>
        <button
          onClick={scrollRight}
          className="bg-black text-white p-3 rounded-full hover:bg-gray-800 transition"
        >
          <FaChevronRight />
        </button>
      </div>

      {/* Cards Container */}
      <div
        ref={sliderRef}
        className="flex space-x-4 overflow-x-auto scroll-smooth py-4 no-scrollbr"
        style={{ scrollbarWidth: "none" }}
      >
        {cards.map((card, index) => (
          <div
            key={index}
            className="min-w-[80%] sm:min-w-[400px] flex-shrink-0 border-2 rounded-lg p-6 sm:p-10 shadow-lg bg-white transform transition-transform duration-300 ease-in-out hover:scale-105 cursor-pointer"
          >
            <h3 className="text-lg font-semibold mb-2">{card.title}</h3>
            <p className="text-sm text-gray-600 mb-4">{card.desc}</p>
            <div className="flex justify-between items-center text-sm font-semibold">
              {card.link}
              <span className="w-6 h-6 rounded-full bg-black text-white flex items-center justify-center">
                <FaChevronRight className="text-xs" />
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Mobile Controls (optional) */}
      <div className="flex justify-center mt-6 md:hidden gap-4">
        <button
          onClick={scrollLeft}
          className="bg-violet-600 text-white p-3 rounded-full"
        >
          <FaChevronLeft />
        </button>
        <button
          onClick={scrollRight}
          className="bg-black text-white p-3 rounded-full"
        >
          <FaChevronRight />
        </button>
      </div>
    </motion.div>
  );
};

export default CardSlider;
