import React, { useState } from "react";

const EndUsers = () => {
  const [search, setSearch] = useState("");
  const [selectedUser, setSelectedUser] = useState(null);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [users, setUsers] = useState([
    {
      id: 1,
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      employeeId: "EMP001",
      designation: "Software Engineer",
      contactNumber: "+91-9876543210",
      dateOfRequest: "2024-01-15"
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      employeeId: "EMP002",
      designation: "Project Manager",
      contactNumber: "+91-9876543211",
      dateOfRequest: "2024-01-16"
    }
  ]);

  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    employeeId: "",
    designation: "",
    contactNumber: "",
    dateOfRequest: new Date().toISOString().split('T')[0]
  });

  const [requisitionForm, setRequisitionForm] = useState({
    itemName: "",
    itemDescription: "",
    quantity: "",
    preferredBrand: "",
    priority: "High",
    purpose: ""
  });

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(search.toLowerCase()) ||
    user.email.toLowerCase().includes(search.toLowerCase())
  );

  const handleAddUser = (e) => {
    e.preventDefault();
    const user = {
      id: users.length + 1,
      ...newUser
    };
    setUsers([...users, user]);
    setNewUser({
      name: "",
      email: "",
      employeeId: "",
      designation: "",
      contactNumber: "",
      dateOfRequest: new Date().toISOString().split('T')[0]
    });
    setShowAddUserModal(false);
  };

  const handleRequisitionSubmit = (e) => {
    e.preventDefault();
    // Handle requisition form submission
    console.log("Requisition submitted:", requisitionForm);
    // Reset form
    setRequisitionForm({
      itemName: "",
      itemDescription: "",
      quantity: "",
      preferredBrand: "",
      priority: "High",
      purpose: ""
    });
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="flex justify-between items-start mb-4">
        <h1 className="text-2xl font-semibold">End-Users</h1>
        <input
          type="text"
          placeholder="Search by user name or email"
          className="border-b border-purple-400 focus:outline-none focus:border-purple-600 text-sm px-1 w-64"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      <div className="flex flex-col lg:flex-row gap-6 bg-white border border-purple-300 rounded-lg p-4 shadow-md">
        {/* Users List */}
        <div className="flex-1">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium">Users</h2>
            <button
              onClick={() => setShowAddUserModal(true)}
              className="text-purple-600 hover:text-purple-800 text-sm font-medium"
            >
              + Add Users
            </button>
          </div>
          
          <div className="border border-purple-200 rounded overflow-y-auto max-h-[400px]">
            {filteredUsers.length > 0 ? (
              filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className={`px-3 py-3 cursor-pointer border-b border-purple-100 hover:bg-purple-50 ${
                    selectedUser?.id === user.id ? "bg-purple-100 font-semibold" : ""
                  }`}
                  onClick={() => setSelectedUser(user)}
                >
                  <div className="font-medium">{user.name}</div>
                  <div className="text-sm text-gray-600">{user.email}</div>
                </div>
              ))
            ) : (
              <div className="px-3 py-4 text-gray-500">No users found.</div>
            )}
          </div>
        </div>

        {/* Purchase Requisition Form */}
        <div className="flex-1">
          <h2 className="text-lg font-medium mb-4">Purchase Requisition Form</h2>
          
          {/* Requester Information */}
          <div className="mb-6">
            <h3 className="text-md font-medium mb-3">Requester Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Employee Name</label>
                <input
                  type="text"
                  value={selectedUser?.name || ""}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Employee ID</label>
                <input
                  type="text"
                  value={selectedUser?.employeeId || ""}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Designation</label>
                <input
                  type="text"
                  value={selectedUser?.designation || ""}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Email</label>
                <input
                  type="email"
                  value={selectedUser?.email || ""}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Contact Number</label>
                <input
                  type="text"
                  value={selectedUser?.contactNumber || ""}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Date of Request</label>
                <input
                  type="date"
                  value={selectedUser?.dateOfRequest || ""}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                />
              </div>
            </div>
          </div>

          {/* Item Details */}
          <form onSubmit={handleRequisitionSubmit}>
            <div className="mb-6">
              <h3 className="text-md font-medium mb-3">Item Details</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Item Name</label>
                  <input
                    type="text"
                    value={requisitionForm.itemName}
                    onChange={(e) => setRequisitionForm({...requisitionForm, itemName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Item Description / Specification</label>
                  <textarea
                    value={requisitionForm.itemDescription}
                    onChange={(e) => setRequisitionForm({...requisitionForm, itemDescription: e.target.value})}
                    rows="3"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Quantity</label>
                    <input
                      type="number"
                      value={requisitionForm.quantity}
                      onChange={(e) => setRequisitionForm({...requisitionForm, quantity: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Preferred Brand/Model</label>
                    <input
                      type="text"
                      value={requisitionForm.preferredBrand}
                      onChange={(e) => setRequisitionForm({...requisitionForm, preferredBrand: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Priority</label>
                    <select
                      value={requisitionForm.priority}
                      onChange={(e) => setRequisitionForm({...requisitionForm, priority: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                    >
                      <option value="High">High</option>
                      <option value="Medium">Medium</option>
                      <option value="Low">Low</option>
                    </select>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Purpose</label>
                  <textarea
                    value={requisitionForm.purpose}
                    onChange={(e) => setRequisitionForm({...requisitionForm, purpose: e.target.value})}
                    rows="3"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                  />
                </div>
              </div>
            </div>

            <button
              type="submit"
              disabled={!selectedUser}
              className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              Submit Requisition
            </button>
          </form>
        </div>
      </div>

      {/* Add User Modal */}
      {showAddUserModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">Add New User</h3>
            <form onSubmit={handleAddUser}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Employee Name</label>
                  <input
                    type="text"
                    required
                    value={newUser.name}
                    onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Email</label>
                  <input
                    type="email"
                    required
                    value={newUser.email}
                    onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Employee ID</label>
                  <input
                    type="text"
                    required
                    value={newUser.employeeId}
                    onChange={(e) => setNewUser({...newUser, employeeId: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Designation</label>
                  <input
                    type="text"
                    required
                    value={newUser.designation}
                    onChange={(e) => setNewUser({...newUser, designation: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Contact Number</label>
                  <input
                    type="tel"
                    required
                    value={newUser.contactNumber}
                    onChange={(e) => setNewUser({...newUser, contactNumber: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowAddUserModal(false)}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                >
                  Add User
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default EndUsers;
