import aboutImg1 from "../../assets/aboutImg1.jpg";
import aboutImg2 from "../../assets/aboutImg2.png";
import aboutImg3 from "../../assets/aboutImg3.jpg";
import { FaUsers, FaUserShield } from "react-icons/fa";
import { BiTransferAlt } from "react-icons/bi";
import Footer from "../../components/Footer";

function AboutPage() {
  return (
    <>
      {/* Section 1 */}
      <section className="mt-16 px-4 sm:px-6 md:px-10 lg:px-20 py-16 text-center bg-white">
        <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight">
          About Our Company
        </h2>
        <p className="text-gray-700 text-lg sm:text-xl lg:text-xl max-w-3xl mx-auto mb-10 leading-relaxed">
          Team RFG is an innovative provider of cloud-based procurement software
          for growing and large-scale organizations.
        </p>

        <div className="relative max-w-6xl mx-auto rounded-xl overflow-hidden">
          {/* Purple Overlay */}
          <div className="absolute inset-0 bg-violet-700 opacity-60 z-10"></div>

          {/* Image */}
          <img
            src={aboutImg1}
            alt="About our company"
            className="w-full h-[16rem] md:h-full object-cover"
          />
        </div>
      </section>

      {/* Section 2 */}
      <section className="px-4 sm:px-6 md:px-10 lg:px-20 py-16 bg-white">
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-stretch gap-10">
          {/* Text Content */}
          <div className="md:w-1/2 flex flex-col justify-center">
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight">
              About Sourcemarkit
            </h2>
            <p className="text-gray-700 text-lg sm:text-xl lg:text-xl leading-relaxed xl:leading-10">
              At Sourcemarkit, we are committed to revolutionizing procurement
              through smart, secure, and scalable technology. Our RFQ Management
              System is purpose-built to help organizations move beyond emails,
              spreadsheets, and manual workflows by providing a unified platform
              that brings structure, speed, and simplicity to every stage of the
              procurement lifecycle.
            </p>
          </div>

          {/* Image */}
          <div className="md:w-1/2">
            <img
              src={aboutImg2}
              alt="Team working around laptops"
              className="w-full h-full object-cover rounded-xl xl:h-[25rem]"
            />
          </div>
        </div>
      </section>

      {/* Section 3 - Our Mission */}
      <section className="px-4 sm:px-6 md:px-10 lg:px-20 py-16 bg-white">
        <div className="max-w-7xl mx-auto flex flex-col-reverse md:flex-row items-stretch gap-10">
          {/* Image */}
          <div className="md:w-1/2">
            <img
              src={aboutImg3}
              alt="Team working on mission"
              className="w-full h-full object-cover rounded-xl"
            />
          </div>

          {/* Text Content */}
          <div className="md:w-1/2 flex flex-col justify-center">
            <h2 className="text-3xl sm:text-4xl md:text-5xl text-center font-bold mb-6 leading-tight text-yellow-500">
              Our Mission
            </h2>
            <p className="text-gray-700 text-lg sm:text-xl lg:text-xl leading-relaxed xl:leading-10">
              To empower businesses with intelligent tools that streamline RFQ
              processes, ensure compliance, and build stronger supplier
              relationships — all while reducing operational complexity and
              cost.
            </p>
          </div>
        </div>
      </section>

      {/* Section 4 - Our Vision */}
      {/* Section 4 - What We Do (Card Style) */}
      <section className="px-4 sm:px-6 md:px-10 lg:px-20 py-16 bg-white">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-12">
            What We Do
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Card 1 */}
            <div className="bg-white shadow-lg rounded-2xl p-6 flex flex-col items-center text-center hover:shadow-xl transition">
              <FaUsers className="text-violet-600 w-10 h-10 mb-4" />
              <h3 className="text-violet-600 text-xl font-semibold mb-2">
                Procurement Teams
              </h3>
              <p className="text-gray-700 text-base leading-relaxed">
                Create and manage Purchase Requisitions, track RFQ status, and
                automate quote evaluations.
              </p>
            </div>

            {/* Card 2 */}
            <div className="bg-white shadow-lg rounded-2xl p-6 flex flex-col items-center text-center hover:shadow-xl transition">
              <FaUserShield className="text-violet-600 w-10 h-10 mb-4" />
              <h3 className="text-violet-600 text-xl font-semibold mb-2">
                Admin Users
              </h3>
              <p className="text-gray-700 text-base leading-relaxed">
                Control access, configure workflows, and manage suppliers
                effortlessly.
              </p>
            </div>

            {/* Card 3 */}
            <div className="bg-white shadow-lg rounded-2xl p-6 flex flex-col items-center text-center hover:shadow-xl transition">
              <BiTransferAlt className="text-violet-600 w-10 h-10 mb-4" />
              <h3 className="text-violet-600 text-xl font-semibold mb-2">
                Suppliers
              </h3>
              <p className="text-gray-700 text-base leading-relaxed">
                Receive, respond to, and track RFQs with a secure and
                user-friendly portal.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Section 5 - Featured by Leading Publications */}
      <section className="px-4 sm:px-6 md:px-10 lg:px-20 py-12 bg-white">
        <div className="max-w-7xl mx-auto flex items-center justify-center">
          <div className="flex items-center w-full gap-4">
            {/* Left Line */}
            <div className="flex-grow h-px bg-gray-300" />

            {/* Title */}
            <h2 className="text-lg sm:text-xl md:text-2xl font-semibold text-center whitespace-nowrap">
              Featured by Leading Publications
            </h2>

            {/* Right Line */}
            <div className="flex-grow h-px bg-gray-300" />
          </div>
        </div>
      </section>

      {/* Section 6 - Savings Form */}
      <section className="px-4 sm:px-6 md:px-10 lg:px-20 py-16 bg-white">
        <div className="max-w-7xl mx-auto flex flex-col lg:flex-row items-center justify-between gap-10">
          {/* Text */}
          <div className="lg:w-1/2 text-center lg:text-left">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold leading-snug">
              How much can you save
              <br />
              with Sourcemarkit e-
              <br />
              procurement platform?
            </h2>
          </div>

          {/* Form */}
          <div className="lg:w-1/2 w-full max-w-md bg-violet-700 rounded-2xl p-6 sm:p-8 shadow-lg">
            <form className="flex flex-col gap-4">
              <input
                type="text"
                placeholder="Your Name*"
                className="px-4 py-2 rounded-md focus:outline-none text-sm text-gray-800"
              />
              <input
                type="tel"
                placeholder="+91"
                className="px-4 py-2 rounded-md focus:outline-none text-sm text-gray-800"
              />
              <input
                type="email"
                placeholder="Work Email*"
                className="px-4 py-2 rounded-md focus:outline-none text-sm text-gray-800"
              />
              <button
                // type="submit"
                className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 rounded-md"
              >
                Submit
              </button>
            </form>
          </div>
        </div>
      </section>

      <Footer />
    </>
  );
}

export default AboutPage;
