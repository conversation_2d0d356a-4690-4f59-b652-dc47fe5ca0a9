import React from "react";
import { FaBell, FaQuestionCircle, FaBriefcase } from "react-icons/fa";
import { NavLink } from "react-router-dom";
import LogoTopBar from "../assets/LogoTopBar.png";
import { IoMdAdd } from "react-icons/io";

const TopBar = () => {
  return (
    <div className="w-3/4 fixed top-16 left-1/2 transform -translate-x-1/2 bg-white shadow border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 md:px-6 h-16 flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center space-x-6">
          {/* Logo or App Icon */}
          <div className="text-purple-600 text-2xl">
            <img
              alt="Logo"
              className="h-8 w-8 rounded-full"
              src={LogoTopBar} // Replace with your logo path
            />
          </div>

          {/* Navigation */}
          <NavLink to="/create">
            <button className="text-white bg-purple-500 hover:bg-purple-600 px-3 py-1 rounded text-sm flex items-center gap-1">
              <IoMdAdd /> Create
            </button>
          </NavLink>
          <NavLink
            to="/events"
            className={({ isActive }) =>
              `text-sm ${
                isActive
                  ? "underline text-purple-700 font-semibold"
                  : "text-black"
              }`
            }
          >
            Events
          </NavLink>
          <NavLink
            to="/admin"
            className={({ isActive }) =>
              `text-sm ${
                isActive
                  ? "underline text-purple-700 font-semibold"
                  : "text-black"
              }`
            }
          >
            Administration
          </NavLink>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-5 text-gray-800">
          <FaQuestionCircle className="cursor-pointer" />
          <FaBriefcase className="cursor-pointer" />
          <FaBell className="cursor-pointer" />
          <div className="text-sm font-semibold bg-gray-100 px-2 py-1 rounded">
            99+
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopBar;
