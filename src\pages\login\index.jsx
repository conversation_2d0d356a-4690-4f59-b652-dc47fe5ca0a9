import { FaUser } from "react-icons/fa";
import { <PERSON> } from "react-router-dom";

const LoginSection = () => {
  return (
    <div className="flex flex-col md:flex-row items-center justify-center min-h-screen px-4 py-10 bg-white">
      {/* Left Side Text */}
      <div className="md:w-1/2 mb-10 md:mb-0 md:pr-10">
        <h2 className="text-3xl md:text-4xl font-bold text-purple-700 mb-4">
          Welcome back!
        </h2>
        <p className="text-gray-800 mb-4">
          Login to your account using your email and password.
        </p>
        <div className="text-sm space-y-3">
          <p>
            <span className="font-semibold">For Procurement teams:</span>{" "}
            <a href="#" className="text-blue-600 underline">
              Request
            </a>{" "}
            a demo to see how Sourcemarkit can transform your buying process.
          </p>
          <p>
            <span className="font-semibold">For Suppliers:</span>{" "}
            <a href="#" className="text-blue-600 underline">
              Register
            </a>{" "}
            for a Sourcemarkit supplier account to grow your business with
            Sourcemarkit.
          </p>
        </div>
      </div>

      {/* Right Side Form */}
      <div className="relative md:w-1/2 max-w-md w-full">
        {/* Background Accent */}
        <div className="absolute top-2 left-2 w-full h-full bg-purple-600 rounded-xl -z-10"></div>

        <div className="bg-indigo-100 rounded-xl p-8 shadow-md">
          <form className="space-y-5">
            <div>
              <label className="block text-sm font-medium mb-1">Email</label>
              <input
                type="email"
                className="w-full px-4 py-2 border border-purple-400 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Password</label>
              <input
                type="password"
                className="w-full px-4 py-2 border border-purple-400 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div className="flex items-center justify-between">
              <button
                type="submit"
                className="flex items-center gap-2 bg-purple-600 text-white px-5 py-2 rounded-md hover:bg-purple-700 transition"
              >
                <FaUser className="text-white" />
                LOGIN
              </button>
              <a
                href="#"
                className="text-sm text-black font-medium hover:underline"
              >
                Lost password?
              </a>
            </div>
          </form>
          <p className="text-sm text-center mt-6 text-black">
            Not using Sourcemarkit yet?{" "}
            <Link to="/register" className="text-blue-600 underline">
              Register
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginSection;
