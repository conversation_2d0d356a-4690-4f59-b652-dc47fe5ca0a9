// import React from "react";
// import {
//   FaXTwitter,
//   FaFacebook,
//   FaInstagram,
//   FaLinkedin,
// } from "react-icons/fa6";

// const Footer = () => {
//   return (
//     <footer className="bg-gradient-to-b from-[#5D33F6] to-[#3e1cb3] text-white pt-16 pb-10 px-6 md:px-20">
//       <div className="mx-auto text-center">
//         {/* Top CTA Section */}
//         <h2 className="text-5xl md:text-5xl font-semibold">
//           The only RFQ platform built
//         </h2>
//         <p className="text-5xl font-semibold">
//           for to scale with your
//           <br /> business.
//         </p>

//         <button className="mt-6 px-6 py-2 bg-white text-primary font-semibold rounded-full hover:opacity-90 transition">
//           Get Started
//         </button>

//         {/* Divider */}
//         <hr className="my-12 border-white/30" />

//         {/* Footer Links Grid */}
//         <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-left text-sm justify-items-center mx-auto">
//           {/* Socials */}
//           <div className="flex items-center justify-center md:justify-start gap-4">
//             <FaXTwitter className="w-6 h-6 bg-white text-black p-1 rounded-full" />
//             <FaFacebook className="w-6 h-6 bg-white text-black p-1 rounded-full" />
//             <FaInstagram className="w-6 h-6 bg-white text-black p-1 rounded-full" />
//             <FaLinkedin className="w-6 h-6 bg-white text-black p-1 rounded-full" />
//           </div>

//           {/* Features */}
//           <div>
//             <h4 className="font-bold mb-2">FEATURES</h4>
//             <ul className="space-y-1">
//               <li>Master Admin Panels</li>
//               <li>Client Admin Panels</li>
//               <li>Supplier Management</li>
//               <li>Purchase Requisition (PR) Workflow</li>
//               <li>RFQ Lifecycle Automation</li>
//               <li>Supplier Portal & Reminders</li>
//               <li>Order Generation Integration</li>
//             </ul>
//           </div>

//           {/* Resources */}
//           <div>
//             <h4 className="font-bold mb-2">RESOURCES</h4>
//             <ul className="space-y-1">
//               <li>Blog</li>
//               <li>FAQs</li>
//             </ul>
//           </div>

//           {/* About */}
//           <div>
//             <h4 className="font-bold mb-2">ABOUT</h4>
//             <ul className="space-y-1">
//               <li>Who we are</li>
//               <li>Contact us</li>
//               <li>Partners</li>
//             </ul>
//           </div>
//         </div>
//       </div>
//     </footer>
//   );
// };

// export default Footer;

import React from "react";
import {
  FaXTwitter,
  FaFacebook,
  FaInstagram,
  FaLinkedin,
} from "react-icons/fa6";

const Footer = () => {
  return (
    <footer className="bg-gradient-to-b from-[#5D33F6] to-[#3e1cb3] text-white pt-16 pb-10 px-4 sm:px-6 md:px-20">
      <div className="max-w-7xl mx-auto text-center space-y-8">
        {/* CTA Section */}
        <div>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-semibold leading-tight">
            The only RFQ platform built
          </h2>
          <p className="text-2xl sm:text-3xl md:text-5xl font-semibold mt-2">
            to scale with your <br className="hidden sm:block" /> business.
          </p>
          <button className="mt-6 px-6 py-2 bg-white text-[#5D33F6] font-semibold rounded-full hover:opacity-90 transition">
            Get Started
          </button>
        </div>

        {/* Divider */}
        <hr className="border-white/30" />

        {/* Footer Links */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 text-left text-sm">
          {/* Social Icons */}
          <div className="flex flex-wrap justify-center gap-4 md:gap-2 lg:md-4 items-center">
            <FaXTwitter className="w-6 h-6 bg-white text-black p-1 rounded-full" />
            <FaFacebook className="w-6 h-6 bg-white text-black p-1 rounded-full" />
            <FaInstagram className="w-6 h-6 bg-white text-black p-1 rounded-full" />
            <FaLinkedin className="w-6 h-6 bg-white text-black p-1 rounded-full" />
          </div>

          {/* Features */}
          <div className="text-center">
            <h4 className="font-bold mb-2">FEATURES</h4>
            <ul className="space-y-1">
              <li>Master Admin Panels</li>
              <li>Client Admin Panels</li>
              <li>Supplier Management</li>
              <li>Purchase Requisition (PR) Workflow</li>
              <li>RFQ Lifecycle Automation</li>
              <li>Supplier Portal & Reminders</li>
              <li>Order Generation Integration</li>
            </ul>
          </div>

          {/* Resources */}
          <div className="text-center">
            <h4 className="font-bold mb-2">RESOURCES</h4>
            <ul className="space-y-1">
              <li>Blog</li>
              <li>FAQs</li>
            </ul>
          </div>

          {/* About */}
          <div className="text-center">
            <h4 className="font-bold mb-2">ABOUT</h4>
            <ul className="space-y-1">
              <li>Who we are</li>
              <li>Contact us</li>
              <li>Partners</li>
            </ul>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
