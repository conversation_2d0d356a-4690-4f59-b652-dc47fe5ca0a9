import React, { useState, useRef } from "react";
import Logo from "../assets/logo.png";
import { Link } from "react-router-dom";

const Header = () => {
  const [openDropdown, setOpenDropdown] = useState(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const menuRef = useRef();

  // Close dropdown when clicking outside
  // useEffect(() => {
  //   const handleClickOutside = (e) => {
  //     if (menuRef.current && !menuRef.current.contains(e.target)) {
  //       setOpenDropdown(null);
  //     }
  //   };
  //   document.addEventListener("mousedown", handleClickOutside);
  //   return () => document.removeEventListener("mousedown", handleClickOutside);
  // }, []);

  const toggleDropdown = (menu) => {
    if (openDropdown === menu) {
      setOpenDropdown(null);
    } else {
      setOpenDropdown(menu);
    }
  };

  const navItems = ["Features", "Use Case", "Resources"];

  return (
    <header className="w-full bg-white shadow-sm z-50 fixed top-0 h-16 left-0">
      <div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex flex-wrap md:flex-nowrap items-center justify-between gap y-4"
        ref={menuRef}
      >
        {/* Logo */}
        <div className="flex items-center">
          <Link to="/">
            <img
              src={Logo}
              alt="Sourcemarkit Logo"
              className="h-8 sm:h-10 w-auto max-w-[160px] flex-shrink-0"
            />
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label="Toggle Mobile Menu"
          >
            {mobileMenuOpen ? (
              <svg
                className="w-6 h-6 text-gray-800"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            ) : (
              <svg
                className="w-6 h-6 text-gray-800"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            )}
          </button>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6 lg:space-x-10 text-base md:text-sm lg:text-base font-medium text-gray-800">
          {navItems.map((item) => (
            <div key={item} className="relative">
              <button
                onClick={() => toggleDropdown(item)}
                className="flex items-center space-x-1 hover:text-purple-600 focus:outline-none"
              >
                <span>{item}</span>
                <svg
                  className={`w-4 h-4 transition-transform ${
                    openDropdown === item ? "rotate-180" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {openDropdown === item && (
                <div className="absolute left-0 mt-2 w-44 bg-white border rounded-md shadow-lg z-30">
                  <ul className="py-2 text-sm text-gray-700">
                    <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer">
                      Option 1
                    </li>
                    <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer">
                      Option 2
                    </li>
                    <li className="px-4 py-2 hover:bg-gray-100 cursor-pointer">
                      Option 3
                    </li>
                  </ul>
                </div>
              )}
            </div>
          ))}
          <div className="relative">
            <Link to="/about">
              <button
                className="flex items-center space-x-1 hover:text-purple-600 focus:outline-none"
              >
                <span>About</span>
                <svg
                  className={`w-4 h-4 transition-transform`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
            </Link>
          </div>
          <div className="relative">
            <Link to="/enduser">
              <button className="hover:text-purple-600 focus:outline-none">
                <span>End-User</span>
              </button>
            </Link>
          </div>
        </nav>

        {/* Desktop CTA Buttons */}
        <div className="hidden md:flex items-center space-x-4">
          <Link to="/login">
            <button className="flex items-center px-4 md:px-2 lg:px-4 py-2 md:py-1 lg:py-2 text-sm md:text-xs lg:text-sm font-medium text-purple-600 border border-purple-600 rounded hover:bg-purple-50 transform transition-transform duration-300 ease-in-out hover:scale-105 cursor-pointer">
              LOGIN
              <svg
                className="w-4 h-4 ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 12h14M12 5l7 7-7 7"
                />
              </svg>
            </button>
          </Link>
          <button className="px-4 md:px-2 lg:px-4 py-2 md:py-1 lg:py-2 text-sm md:text-xs lg:text-sm font-medium text-white bg-purple-600 rounded hover:opacity-90 transform transition-transform duration-300 ease-in-out hover:scale-105 cursor-pointer">
            GET A DEMO
          </button>
        </div>
      </div>

      {/* Mobile Dropdown Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden px-4 pb-6 bg-white border-t border-gray-100 shadow">
          <div className="flex flex-col text-sm font-medium text-gray-800 mt-4 space-y-2">
            {navItems.map((item) => (
              <div key={item} className="flex flex-col">
                <button
                  onClick={() => toggleDropdown(item)}
                  className="flex justify-between items-center py-2 hover:text-purple-600"
                >
                  <span>{item}</span>
                  <svg
                    className={`w-4 h-4 transform transition-transform ${
                      openDropdown === item ? "rotate-180" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>

                {openDropdown === item && (
                  <div className="ml-4 mt-1 border-l border-gray-200 pl-4">
                    <ul className="flex flex-col space-y-2 text-sm text-gray-700">
                      <li className="hover:text-purple-600 cursor-pointer">
                        Option 1
                      </li>
                      <li className="hover:text-purple-600 cursor-pointer">
                        Option 2
                      </li>
                      <li className="hover:text-purple-600 cursor-pointer">
                        Option 3
                      </li>
                    </ul>
                  </div>
                )}
              </div>
            ))}
            <div className="py-2">
              <Link to="/about" className="hover:text-purple-600">
                About
              </Link>
            </div>
            <div className="py-2">
              <Link to="/enduser" className="hover:text-purple-600">
                End-User
              </Link>
            </div>
          </div>

          <div className="mt-6 flex flex-col space-y-3">
            <button className="px-4 py-2 text-sm font-medium text-purple-600 border border-purple-600 rounded hover:bg-purple-50">
              LOGIN
            </button>
            <button className="px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded hover:opacity-90">
              GET A DEMO
            </button>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
