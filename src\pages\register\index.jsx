import { Link } from "react-router-dom";

const RegisterSection = () => {
  return (
    <div className="flex flex-col md:flex-row items-center justify-center min-h-screen px-4 py-10 bg-white">
      {/* Left Side Text */}
      <div className="md:w-1/2 mb-10 md:mb-0 md:pr-10">
        <h2 className="text-3xl md:text-4xl font-bold text-purple-700 mb-4">
          Get a free account
        </h2>
        <p className="text-gray-800">
          Create a supplier account to access and manage quotes on Sourcemarkit
        </p>
      </div>

      {/* Right Side Form */}
      <div className="relative md:w-1/2 max-w-md w-full">
        {/* Purple Background Shadow */}
        <div className="absolute top-2 left-2 w-full h-full bg-purple-600 rounded-xl -z-10"></div>

        <div className="bg-indigo-100 rounded-xl p-8 shadow-md">
          <form className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                Company Name<span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                required
                className="w-full px-4 py-2 border border-purple-400 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                Contact First Name<span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                required
                className="w-full px-4 py-2 border border-purple-400 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                Contact Last Name
              </label>
              <input
                type="text"
                className="w-full px-4 py-2 border border-purple-400 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                Contact Phone<span className="text-red-500">*</span>
              </label>
              <input
                type="tel"
                required
                className="w-full px-4 py-2 border border-purple-400 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <p className="text-xs text-gray-700 mt-2">
              By registering you agree to the{" "}
              <a href="#" className="text-blue-600 underline">
                Terms of Use
              </a>{" "}
              and have read the{" "}
              <a href="#" className="text-blue-600 underline">
                Privacy Policy
              </a>
              .
            </p>
            <button
              type="submit"
              className="w-full bg-purple-600 text-white py-2 rounded-md mt-2 hover:bg-purple-700 transition"
            >
              CREATE ACCOUNT
            </button>
          </form>
          <p className="text-sm text-center mt-6 text-black">
            Already have an account?{" "}
            <Link to="/login" className="text-blue-600 underline">
              LogIn
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default RegisterSection;
