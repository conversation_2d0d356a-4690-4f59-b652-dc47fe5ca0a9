import React, { useState } from "react";

const CategoriesPage = () => {
  const [categories] = useState([
    "Office Supplies",
    "Electronics",
    "Furniture",
    "Stationery",
    "Cleaning",
    "Catering",
    "Hardware",
    "Software",
    "Utilities",
    "Transport",
  ]);

  const [selectedCategory, setSelectedCategory] = useState(null);
  const subcategories = {
    "Office Supplies": ["Pens", "Paper", "Folders"],
    Electronics: ["Laptops", "Monitors", "Printers"],
    Furniture: ["Chairs", "Desks", "Cabinets"],
    Stationery: ["Sticky Notes", "Staplers"],
    Cleaning: ["Mops", "Detergents"],
    Catering: ["Snacks", "Drinks"],
    Hardware: ["Tools", "Bolts"],
    Software: ["Accounting", "Security"],
    Utilities: ["Electricity", "Internet"],
    Transport: ["Vehicles", "Fuel"],
  };

  return (
    <div className="p-4 sm:p-6">
      <h1 className="text-2xl font-semibold mb-4">Categories</h1>

      <div className="flex flex-col lg:flex-row gap-4 bg-white border border-purple-300 rounded-lg p-4 shadow-md">
        {/* Category List */}
        <div className="flex-1 border border-purple-200 rounded overflow-y-auto max-h-[400px]">
          <div className="bg-purple-50 px-3 py-2 border-b border-purple-300 font-medium">
            Category
          </div>
          {categories.map((cat, i) => (
            <div
              key={i}
              className={`px-3 py-2 cursor-pointer border-b border-purple-100 hover:bg-purple-50 ${
                selectedCategory === cat ? "bg-purple-100 font-semibold" : ""
              }`}
              onClick={() => setSelectedCategory(cat)}
            >
              {cat}
            </div>
          ))}
        </div>

        {/* Arrow for visual indication */}
        <div className="hidden lg:flex items-center justify-center text-purple-400 text-2xl px-2">
          <span>&#x276F;</span>
        </div>

        {/* Subcategory */}
        <div className="flex-1 border border-purple-200 rounded overflow-y-auto max-h-[400px]">
          <div className="bg-purple-50 px-3 py-2 border-b border-purple-300 font-medium">
            Subcategory
          </div>
          {selectedCategory && subcategories[selectedCategory] ? (
            subcategories[selectedCategory].map((sub, i) => (
              <div
                key={i}
                className="px-3 py-2 border-b border-purple-100 hover:bg-purple-50"
              >
                {sub}
              </div>
            ))
          ) : (
            <div className="px-3 py-4 text-gray-500">No Subcategory</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CategoriesPage;
