import {
  LockClosedIcon,
  ShieldCheckIcon,
  CogIcon,
  DotsHorizontalIcon,
} from "@heroicons/react/solid";
import dataEncryptionImage from "../assets/dataEncryption.png";
import modularImg from "../assets/modularImg.png";
import rbacImg from "../assets/rbacImg.png";
import { motion } from "framer-motion";

const features = [
  {
    title: "Data Encryption",
    description: "Your data is always protected with end-to-end encryption.",
    icon: <LockClosedIcon className="w-5 h-5 mr-2 text-black" />,
    bg: "from-[#e3d2ff] to-[#c9f0ff]",
    image: dataEncryptionImage,
  },
  {
    title: "RBAC & Audit Logs",
    description:
      "Control access with role-based permissions and track every action.",
    icon: <ShieldCheckIcon className="w-5 h-5 mr-2 text-black" />,
    bg: "from-[#e2d7ff] to-[#cfe4ff]",
    image: rbacImg,
  },
  {
    title: "Modular & Scalable Architecture",
    description:
      "Grow on your terms with a flexible, plug-and-play system design.",
    icon: <CogIcon className="w-5 h-5 mr-2 text-black" />,
    bg: "from-[#e9dcff] to-[#d2f0ff]",
    image: modularImg,
  },
];

const commonProps = {
  viewport: { once: false, amount: 0.4 }, // Animate once when 60% of element is in view
  transition: { duration: 0.8, ease: "easeOut" },
  className: "p-8 rounded-lg shadow-xl max-w-md text-center mx-auto",
};

export default function SecuritySection() {
  return (
    <>
      {/* Section 1: Features */}
      <motion.section
        initial={{ opacity: 0, y: 50 }} /* Starts invisible and 50px down */
        whileInView={{
          opacity: 1,
          y: 0,
        }} /* Animates to visible and original position */
        {...commonProps}
        className="py-12 px-4 sm:px-6 max-w-7xl mx-auto text-center"
      >
        <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-10">
          Stay Secure, Grow Fearlessly.
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 ">
          {features.map((feature, index) => (
            <div
              key={index}
              className={`rounded-xl shadow-xl p-6 text-left bg-gradient-to-b ${feature.bg} transition-transform duration-300 ease-in-out hover:translate-y-2 hover:shadow-lg`}
            >
              <img
                src={feature.image}
                alt={feature.title}
                className="w-full h-32 object-contain mb-4"
              />
              <div className="flex items-center font-semibold text-lg mb-1">
                {feature.icon}
                {feature.title}
              </div>
              <p className="text-sm sm:text-base text-black/80">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </motion.section>

      {/* Section 2: Interface Demo */}
      <motion.section
        initial={{ opacity: 0, y: 50 }} /* Starts invisible and 50px down */
        whileInView={{
          opacity: 1,
          y: 0,
        }} /* Animates to visible and original position */
        {...commonProps}
        className="w-full px-4 sm:px-6 py-20 bg-white"
      >
        <div className="relative max-w-6xl mx-auto flex flex-col items-center">
          {/* Top Accent */}
          <div
            className="absolute left-4 sm:left-10 w-2/3 sm:w-1/2 bg-indigo-600 py-2 rounded-t-md z-0"
            style={{
              borderTopLeftRadius: "0.75rem",
              borderTopRightRadius: "0.75rem",
              clipPath: "polygon(0 0, 98% 0, 100% 100%, 0% 100%)",
              top: "-1rem",
            }}
          ></div>

          {/* Main Box */}
          <div className="relative w-full bg-indigo-100 rounded-xl z-10 flex flex-col items-center justify-start pt-10 px-4 sm:px-10 py-6">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-semibold text-black mb-6 text-center">
              Experience the Interface
            </h2>
            <div className="w-full sm:w-[90%] h-[250px] sm:h-[350px] bg-white border border-black/20 rounded-md shadow-md"></div>
          </div>

          {/* Bottom Accent */}
          <div
            className="absolute bottom-0 right-4 sm:right-10 w-2/3 sm:w-1/2 bg-indigo-600 py-2 rounded-b-md z-0"
            style={{
              clipPath: "polygon(2% 0, 100% 0, 100% 100%, 0% 100%)",
              bottom: "-1rem",
            }}
          ></div>
        </div>
      </motion.section>

      {/* Section 3: Testimonial */}
      <motion.section
        initial={{ opacity: 0, y: 50 }} /* Starts invisible and 50px down */
        whileInView={{
          opacity: 1,
          y: 0,
        }} /* Animates to visible and original position */
        {...commonProps}
        className="w-full max-w-7xl mx-auto px-4 sm:px-6 py-16"
      >
        <div className="flex flex-col md:flex-row items-center justify-between gap-10">
          {/* Left */}
          <div className="text-left md:w-1/3">
            <h2 className="text-2xl sm:text-3xl md:text-5xl font-semibold leading-snug text-black">
              Real Voices.
              <br />
              Real Results.
              <br />
              RFQ Made Easy.
            </h2>
          </div>

          {/* Right */}
          <div className="md:w-2/3 w-full relative bg-white rounded-lg shadow-md p-6">
            <div className="absolute top-4 right-4 text-indigo-600">
              <DotsHorizontalIcon className="w-6 h-6 sm:w-8 sm:h-8" />
            </div>
            <div className="flex items-center mb-4">
              <img
                src={modularImg}
                alt="Eric Bremer"
                className="w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 border-indigo-500 mr-4"
              />
              <div>
                <p className="font-semibold text-black">Eric Bremer</p>
                <p className="text-sm italic text-gray-600">
                  Director Electronics & Electrical Products | Corporate Supply
                  Chain – <span className="font-semibold">Emerson</span>
                </p>
              </div>
            </div>
            <p className="text-gray-700 text-sm sm:text-base">
              “Implementation of the Part Analytics’ RFQ software platform has
              driven approximately 90% efficiency gain in our Quarterly Pricing
              Process by automating our manual process.”
            </p>
          </div>
        </div>
      </motion.section>
    </>
  );
}
