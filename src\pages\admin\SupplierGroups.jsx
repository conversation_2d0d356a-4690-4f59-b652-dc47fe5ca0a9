import React, { useState } from "react";

const SupplierGroupsPage = () => {
  const [search, setSearch] = useState("");
  const [groups] = useState([
    "Electronics",
    "Furniture",
    "Clothing",
    "Automotive",
    "Healthcare",
    "Books",
    "Appliances",
    "Sports",
    "Toys",
    "Office Supplies",
  ]);

  const [selectedGroup, setSelectedGroup] = useState(null);

  const suppliersMap = {
    Electronics: ["Samsung", "Apple", "Sony"],
    Furniture: ["IKEA", "Wayfair"],
    Clothing: ["Zara", "H&M"],
    Automotive: ["Ford", "Toyota"],
    Healthcare: ["Pfizer", "Johnson & Johnson"],
    Books: ["Penguin", "HarperCollins"],
    Appliances: ["Whirlpool", "Bosch"],
    Sports: ["Nike", "Adidas"],
    Toys: ["Lego", "Mattel"],
    "Office Supplies": ["Staples", "Office Depot"],
  };

  const filteredGroups = groups.filter((group) =>
    group.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="p-4 sm:p-6">
      <div className="flex justify-between items-start mb-4 flex-wrap gap-2">
        <h1 className="text-2xl font-semibold">Suppliers group</h1>
        <input
          type="text"
          placeholder="Search supplier group"
          className="border-b border-purple-400 focus:outline-none focus:border-purple-600 text-sm px-1 w-64"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        {/* Group List */}
        <div className="flex-1 border border-purple-300 rounded-lg shadow-md overflow-y-auto max-h-[400px]">
          <div className="bg-purple-50 px-3 py-2 border-b border-purple-300 font-medium">
            Group
          </div>
          {filteredGroups.length > 0 ? (
            filteredGroups.map((group, i) => (
              <div
                key={i}
                className={`px-3 py-2 border-b border-purple-100 cursor-pointer hover:bg-purple-100 ${
                  selectedGroup === group ? "bg-purple-100" : ""
                }`}
                onClick={() => setSelectedGroup(group)}
              >
                {group}
              </div>
            ))
          ) : (
            <div className="px-3 py-4 text-gray-500">No groups found.</div>
          )}
        </div>

        {/* Suppliers of selected group */}
        <div className="flex-1 border border-purple-300 rounded-lg shadow-md overflow-y-auto max-h-[400px]">
          <div className="bg-purple-50 px-3 py-2 border-b border-purple-300 font-medium">
            Supplier
          </div>
          {selectedGroup ? (
            suppliersMap[selectedGroup]?.map((supplier, i) => (
              <div
                key={i}
                className="px-3 py-2 border-b border-purple-100 hover:bg-purple-50"
              >
                {supplier}
              </div>
            ))
          ) : (
            <div className="px-3 py-4 text-gray-500">No group selected.</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SupplierGroupsPage;
