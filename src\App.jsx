import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Header from "./components/Header";

import Home from "./pages/index";
import Features from "./pages/features/index";

import About from "./pages/about/index";
import AdminPanel from "./pages/admin";
import AdminLayout from "./layouts/AdminLayout";
import AdminHomeRedirect from "./pages/admin";
import Addresses from "./pages/admin/Addresses";
import Categories from "./pages/admin/Categories";
import Suppliers from "./pages/admin/Suppliers";
import SupplierGroups from "./pages/admin/SupplierGroups";
import Projects from "./pages/admin/Projects";
import Events from "./pages/admin/Events";
import EventLayout from "./layouts/EventLayout";
import Create from "./pages/admin/Create";
import LoginSection from "./pages/login";
import RegisterSection from "./pages/register";

function App() {
  return (
    <Router>
      <Header />
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/features" element={<Features />} />
        <Route path="/about" element={<About />} />
        <Route path="/login" element={<LoginSection />} />
        <Route path="/login" element={<LoginSection />} />
        <Route path="/register" element={<RegisterSection />} />
        {/* Admin Routes */}
        <Route path="/admin" element={<AdminLayout />}>
          <Route index element={<AdminHomeRedirect />} />
          <Route path="addresses" element={<Addresses />} />
          <Route path="categories" element={<Categories />} />
          <Route path="suppliers" element={<Suppliers />} />
          <Route path="suppliergroups" element={<SupplierGroups />} />
          <Route path="projects" element={<Projects />} />
        </Route>
        <Route element={<EventLayout />}>
          <Route path="/events" element={<Events />} />
          <Route path="/create" element={<Create />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;
