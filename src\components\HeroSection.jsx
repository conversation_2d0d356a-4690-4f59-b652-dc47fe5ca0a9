import heroBg from "../assets/hero-bg.png";
import iconImage from "../assets/iconImage.png";
import img2 from "../assets/img2.jpg";
import {
  FaUserShield,
  FaSitemap,
  FaClock,
  FaSyncAlt,
  FaRegFileAlt,
} from "react-icons/fa";
import { MdManageAccounts } from "react-icons/md";
import { motion } from "framer-motion";
import { view } from "framer-motion/client";

const fadeVariants = {
  hidden: { opacity: 0 },
  show: { opacity: 1, transition: { duration: 0.8 } },
  exit: { opacity: 0, transition: { duration: 0.5 } },
};

const commonProps = {
  viewport: { once: false, amount: 0.4 }, // Animate once when 60% of element is in view
  transition: { duration: 0.8, ease: "easeOut" },
  className: "p-8 rounded-lg shadow-xl max-w-md text-center mx-auto",
};

const HeroSection = () => {
  const features = [
    {
      icon: <FaUserShield className="text-xl md:text-2xl" />,
      label: "Master & Client Admin Panels",
      color: "bg-orange-100 text-orange-600",
    },
    {
      icon: <MdManageAccounts className="text-xl md:text-2xl" />,
      label: "Supplier Management",
      color: "bg-rose-100 text-rose-600",
    },
    {
      icon: <FaSitemap className="text-xl md:text-2xl" />,
      label: "Purchase Requisition (PR) Workflow",
      color: "bg-yellow-100 text-yellow-600",
    },
    {
      icon: <FaClock className="text-xl md:text-2xl" />,
      label: "Supplier Portal & Reminders",
      color: "bg-cyan-100 text-cyan-600",
    },
    {
      icon: <FaSyncAlt className="text-xl md:text-2xl" />,
      label: "RFQ Lifecycle Automation",
      color: "bg-blue-100 text-blue-600",
    },
    {
      icon: <FaRegFileAlt className="text-xl md:text-2xl" />,
      label: "Order Generation Integration",
      color: "bg-purple-100 text-purple-600",
    },
  ];

  return (
    <>
      {/* Hero Background Section with fade animation */}
      <motion.div
        initial="hidden"
        animate="show"
        exit="exit"
        variants={fadeVariants}
        className="relative w-full bg-cover bg-center"
        style={{ backgroundImage: `url(${heroBg})` }}
      >
        <div className="absolute inset-0 bg-[rgba(93,51,246,0.6)] z-0" />
        <div className="relative z-10 flex flex-col justify-center min-h-[80vh] px-4 py-20 md:px-20 text-white">
          <div className="p-4 text-center ">
            <h1 className="pt-10 text-3xl md:text-5xl font-bold leading-tight">
              Simplify Your Procurement Process with Our
            </h1>
            <h2 className="text-xl md:text-4xl font-bold mt-2">
              RFQ Management System
            </h2>
            <p className="mt-4 text-sm md:text-lg text-white/90 font-medium">
              Automate your RFQ lifecycle – from requisition to award.
            </p>
            <div className="mt-6 flex flex-wrap gap-4 justify-center">
              <button className="px-5 py-2 border border-white text-white rounded hover:bg-white hover:text-purple-700 transform transition-transform duration-300 ease-in-out hover:scale-105 cursor-pointer">
                CONTACT US
              </button>
              <button className="px-5 py-2 bg-primary text-white rounded hover:opacity-80 transform transition-transform duration-300 ease-in-out hover:scale-105 cursor-pointer">
                GET A DEMO
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Icon + Description */}
      <motion.section
        className="grid grid-cols-1 md:grid-cols-5 items-center gap-6 px-4 md:px-20 py-12 bg-white"
        initial={{ opacity: 0, scale: 0.8 }}
        whileInView={{ opacity: 1, scale: 1 }}
        viewport={{ once: true, amount: 0.6 }}
        transition={{ duration: 1, ease: "easeInOut" }}
      >
        <div className="md:col-span-2 flex justify-center">
          <img
            src={iconImage}
            alt="RFQ Icon"
            className="w-24 h-24 md:w-40 md:h-40 object-contain"
          />
        </div>
        <div className="md:col-span-3 text-center md:text-left">
          <h2 className="text-xl md:text-4xl font-semibold text-black leading-snug">
            RFQ management system eases <br />
            <span className="font-semibold">deadline stress.</span>
          </h2>
          <p className="mt-3 text-sm md:text-xl text-gray-700">
            RFQ Management System simplifies complex bids, enabling timely,
            accurate submissions with built-in automation and collaboration
            tools.
          </p>
        </div>
      </motion.section>

      {/* Features Section Header */}
      <motion.div
        initial={{ opacity: 0, y: 50 }} /* Starts invisible and 50px down */
        whileInView={{
          opacity: 1,
          y: 0,
        }} /* Animates to visible and original position */
        {...commonProps}
        className="text-center px-4 md:px-0 py-12"
      >
        <h2 className="text-xl md:text-4xl font-semibold text-black leading-snug">
          The RFQ Platform for Every Team in Your{" "}
          <br className="hidden md:block" />
          Procurement Workflow
        </h2>
        <p className="mt-3 text-sm md:text-lg text-gray-700">
          Explore how the key features work without signing up for a trial or
          demo.
        </p>
      </motion.div>

      {/* Feature List & Image */}
      <motion.div
        initial={{ opacity: 0, y: -50 }} /* Starts invisible and 50px up */
        whileInView={{
          opacity: 1,
          y: 0,
        }} /* Animates to visible and original position */
        {...commonProps}
        className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center px-4 md:px-20 py-16"
      >
        {/* Feature List */}
        <div className="space-y-5 w-full max-w-xl mx-auto ">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center gap-4">
              <div className={`p-3 rounded-md ${feature.color}`}>
                {feature.icon}
              </div>
              <div>
                <p className="text-base md:text-xl lg:text-2xl xl:text-3xl text-black font-medium">
                  {feature.label}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Feature Illustration */}
        <div className="relative w-full max-w-xl mx-auto md:mx-0">
          <div
            className="w-2/4 bg-indigo-600 py-2 rounded-t-md ml-10"
            style={{
              clipPath: "polygon(0 0, 95% 0, 100% 100%, 0% 100%)",
            }}
          />
          <img
            src={img2}
            alt="Feature Illustration"
            className="w-full h-auto object-contain"
          />
          <div
            className="w-2/4 bg-indigo-600 py-2 rounded-b-md ml-auto mr-10"
            style={{
              clipPath: "polygon(5% 0, 100% 0, 100% 100%, 0% 100%)",
            }}
          />
        </div>
      </motion.div>
    </>
  );
};

export default HeroSection;
