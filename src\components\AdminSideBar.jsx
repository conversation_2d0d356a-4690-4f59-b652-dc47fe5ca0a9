import React from "react";
import { NavLink } from "react-router-dom";
import {
  FaAddressBook,
  FaTh,
  FaUsers,
  FaLayerGroup,
  FaBoxes,
} from "react-icons/fa";

const menuItems = [
  { label: "Addresses", icon: FaAddressBook, path: "/admin/addresses" },
  { label: "Categories", icon: FaTh, path: "/admin/categories" },
  { label: "Suppliers", icon: FaUsers, path: "/admin/suppliers" },
  {
    label: "Supplier Group",
    icon: FaLayerGroup,
    path: "/admin/suppliergroups",
  },
  { label: "Projects", icon: FaBoxes, path: "/admin/projects" },
];

const Sidebar = () => {
  return (
    <aside className="w-full md:w-60 bg-white border-r border-gray-200 px-4 py-6">
      <nav className="space-y-2 text-sm font-medium text-gray-800">
        {menuItems.map(({ label, icon, path }) => {
          const Icon = icon;
          return (
            <NavLink
              key={label}
              to={path}
              className={({ isActive }) =>
                `flex items-center space-x-2 p-2 rounded hover:bg-purple-50 cursor-pointer ${
                  isActive ? "bg-purple-100 text-purple-600" : ""
                }`
              }
            >
              <Icon className="text-lg" />
              <span>{label}</span>
            </NavLink>
          );
        })}
      </nav>
    </aside>
  );
};

export default Sidebar;
